"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Link from "next/link"

interface MailingListManagerProps {
  creatorId: string
  creatorName: string
  customUrl?: string | null
}

interface Subscriber {
  id: string
  subscriber_email: string
  subscriber_name: string | null
  created_at: string
  notification_preferences: any
}

export function MailingListManager({ creatorId, creatorName, customUrl }: MailingListManagerProps) {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([])
  const [loading, setLoading] = useState(true)
  const [showSendModal, setShowSendModal] = useState(false)
  const [notificationTitle, setNotificationTitle] = useState("")
  const [notificationMessage, setNotificationMessage] = useState("")
  const [sendingNotification, setSendingNotification] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const supabase = createSupabaseClient()
  const mailingListUrl = `/${customUrl || `u/${creatorId}`}/mailing-list`

  useEffect(() => {
    loadSubscribers()
  }, [creatorId])

  const loadSubscribers = async () => {
    try {
      const { data, error } = await supabase
        .from("creator_notification_subscriptions")
        .select("*")
        .eq("creator_id", creatorId)
        .order("created_at", { ascending: false })

      if (error) throw error
      setSubscribers(data || [])
    } catch (err) {
      console.error("Error loading subscribers:", err)
    } finally {
      setLoading(false)
    }
  }

  const calculateNotificationCost = () => {
    const subscriberCount = subscribers.length
    // $0.50 per 100 people, minimum $0.50
    return Math.max(0.50, Math.ceil(subscriberCount / 100) * 0.50)
  }

  const handleSendNotification = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!notificationTitle.trim() || !notificationMessage.trim()) {
      setError("Title and message are required")
      return
    }

    if (subscribers.length === 0) {
      setError("No subscribers to send notifications to")
      return
    }

    setSendingNotification(true)
    setError("")

    try {
      // Create notification campaign
      const { data: campaign, error: campaignError } = await supabase
        .from("notification_campaigns")
        .insert({
          creator_id: creatorId,
          campaign_title: notificationTitle.trim(),
          campaign_message: notificationMessage.trim(),
          recipient_count: subscribers.length,
          cost_cents: Math.round(calculateNotificationCost() * 100),
          notification_status: 'ready_to_send'
        })
        .select()
        .single()

      if (campaignError) throw campaignError

      // TODO: Integrate with payment system for notification credits
      // For now, we'll just create the campaign
      
      setSuccess(`Notification campaign created! Cost: $${calculateNotificationCost().toFixed(2)}`)
      setNotificationTitle("")
      setNotificationMessage("")
      setShowSendModal(false)
      
    } catch (err) {
      console.error("Error sending notification:", err)
      setError("Failed to create notification campaign")
    } finally {
      setSendingNotification(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl p-4 sm:p-6 shadow-sm border border-gray-100">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-6">
        <div>
          <h2 className="text-xl font-serif text-gray-800">Mailing List</h2>
          <p className="text-gray-600 text-sm">
            Manage your subscribers and send notifications
          </p>
        </div>
        <Link
          href={mailingListUrl}
          target="_blank"
          className="text-purple-600 hover:text-purple-700 font-medium text-sm flex-shrink-0"
        >
          View Landing Page →
        </Link>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-6">
        <div className="bg-purple-50 rounded-lg p-4 text-center sm:text-left">
          <div className="text-xl sm:text-2xl font-bold text-purple-600">
            {subscribers.length}
          </div>
          <div className="text-sm text-purple-700">
            {subscribers.length === 1 ? 'Subscriber' : 'Subscribers'}
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4 text-center sm:text-left">
          <div className="text-xl sm:text-2xl font-bold text-blue-600">
            ${calculateNotificationCost().toFixed(2)}
          </div>
          <div className="text-sm text-blue-700">
            Cost to notify all
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4 text-center sm:text-left">
          <div className="text-xl sm:text-2xl font-bold text-green-600">
            📧
          </div>
          <div className="text-sm text-green-700">
            Ready to send
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-3 mb-6">
        <button
          onClick={() => setShowSendModal(true)}
          disabled={subscribers.length === 0}
          className="bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          📧 Send Notification
        </button>
        
        <Link
          href={mailingListUrl}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors text-center"
        >
          📋 Share Landing Page
        </Link>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <p className="text-green-600 text-sm">{success}</p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Recent Subscribers */}
      <div>
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Recent Subscribers
        </h3>
        
        {subscribers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📧</div>
            <p className="text-lg font-medium mb-1">No subscribers yet</p>
            <p className="text-sm">Share your mailing list page to get started!</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {subscribers.slice(0, 10).map((subscriber) => (
              <div key={subscriber.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 p-3 bg-gray-50 rounded-lg">
                <div className="min-w-0 flex-1">
                  <div className="font-medium text-gray-900 truncate">
                    {subscriber.subscriber_name || 'Anonymous'}
                  </div>
                  <div className="text-sm text-gray-600 truncate">
                    {subscriber.subscriber_email}
                  </div>
                </div>
                <div className="text-xs text-gray-500 flex-shrink-0">
                  {new Date(subscriber.created_at).toLocaleDateString()}
                </div>
              </div>
            ))}

            {subscribers.length > 10 && (
              <div className="text-center py-2">
                <span className="text-sm text-gray-500">
                  +{subscribers.length - 10} more subscribers
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Send Notification Modal */}
      {showSendModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-lg w-full p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                Send Notification
              </h3>
              <button
                onClick={() => setShowSendModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-700">
                <strong>Recipients:</strong> {subscribers.length} subscribers
                <br />
                <strong>Cost:</strong> ${calculateNotificationCost().toFixed(2)}
              </div>
            </div>

            <form onSubmit={handleSendNotification} className="space-y-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Notification Title *
                </label>
                <input
                  id="title"
                  type="text"
                  value={notificationTitle}
                  onChange={(e) => setNotificationTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="New story published!"
                  required
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Message *
                </label>
                <textarea
                  id="message"
                  value={notificationMessage}
                  onChange={(e) => setNotificationMessage(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="I just published a new story that I think you'll love..."
                  required
                />
              </div>

              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={() => setShowSendModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={sendingNotification}
                  className="flex-1 bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  {sendingNotification ? 'Creating...' : 'Create Campaign'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
