"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { WithdrawalSection } from "@/components/WithdrawalSection"
import { StripeConnectButton } from "@/components/StripeConnectButton"
import { Button } from "@/components/ui/button"


import { PriceEditor } from "@/components/PriceEditor"
import { EntriesManager } from "@/components/EntriesManager"
import { MailingListManager } from "@/components/MailingListManager"

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

export default function WriterDashboard() {
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<string | null>(null)
  const [stripeBalance, setStripeBalance] = useState<{
    available: number
    pending: number
    message?: string
  }>({ available: 0, pending: 0 })
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthAndFetchData()
  }, [])

  const checkAuthAndFetchData = async () => {
    try {
      // Check if user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("id", user.id)
        .single()

      if (profileError || !profileData) {
        console.log('Profile error or no profile:', profileError, profileData)
        router.push('/')
        return
      }

      console.log('User profile:', { id: profileData.id, role: profileData.role, name: profileData.name })

      // Redirect subscribers to their timeline
      if (profileData.role === 'subscriber') {
        console.log('Redirecting subscriber to timeline')
        router.push('/timeline')
        return
      }

      // Only writers and admins can access this dashboard
      if (profileData.role !== 'writer' && profileData.role !== 'admin') {
        console.log('Access denied - role is:', profileData.role)
        router.push('/')
        return
      }

      console.log('Dashboard access granted for role:', profileData.role)

      // Get writer's entries
      const { data: entries } = await supabase
        .from("diary_entries")
        .select("id, title, body_md, created_at, is_free, is_hidden, view_count")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      // Get subscription count
      const { data: subscriptions } = await supabase
        .from("subscriptions")
        .select("id, subscriber_id, active_until")
        .eq("writer_id", user.id)
        .gte("active_until", new Date().toISOString())

      // Get payments for earnings calculation
      const { data: payments } = await supabase
        .from("payments")
        .select("amount_cents, kind, created_at, stripe_payment_id")
        .eq("writer_id", user.id)
        .order("created_at", { ascending: false })

      console.log('All payments:', payments)

      // Get withdrawals for balance calculation
      const { data: withdrawals } = await supabase
        .from("withdrawals")
        .select("amount_cents, status")
        .eq("writer_id", user.id)
        .neq("status", "rejected")

      setProfile({
        ...profileData,
        entries: entries || [],
        subscriptions: subscriptions || [],
        payments: payments || [],
        withdrawals: withdrawals || []
      })

      // Fetch real Stripe Connect balance
      await fetchStripeBalance()
    } catch (error) {
      console.error('Error in checkAuthAndFetchData:', error)
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const fetchStripeBalance = async () => {
    try {
      console.log('Fetching Stripe balance...')
      const response = await fetch('/api/stripe/balance')
      const data = await response.json()

      console.log('Stripe balance response:', { status: response.status, data })

      if (response.ok) {
        setStripeBalance({
          available: data.available,
          pending: data.pending,
          message: data.message
        })
        console.log('Stripe balance set:', data.available)
      } else {
        console.error('Error fetching Stripe balance:', data.error)
        setStripeBalance({ available: 0, pending: 0, message: data.error })
      }
    } catch (error) {
      console.error('Error fetching Stripe balance:', error)
      setStripeBalance({ available: 0, pending: 0, message: 'Failed to fetch balance' })
    }
  }

  const handleNavigation = async (path: string) => {
    setNavigating(path)
    router.push(path)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Dashboard...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load dashboard</p>
        </div>
      </div>
    )
  }

  const activeSubscriberCount = profile.subscriptions?.length || 0
  const monthlyRevenue = activeSubscriberCount * (profile.price_monthly || 0)
  const totalEntries = profile.entries?.length || 0
  const totalViews = profile.entries?.reduce((sum: number, entry: any) => sum + (entry.view_count || 0), 0) || 0


  // Use real Stripe Connect balance instead of manual calculations
  const availableBalance = stripeBalance.available // This is the actual withdrawable amount from Stripe

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full overflow-x-hidden">
        <div className="w-full max-w-full overflow-hidden px-4 py-4 sm:max-w-7xl sm:mx-auto sm:px-6 lg:px-8">
        {/* Mobile-First Header */}
        <div className="mb-4">
          <h1 className="text-xl font-serif text-gray-900 mb-1 leading-tight">
            Welcome back, {profile.name || 'Writer'}
          </h1>
          <p className="text-gray-600 text-sm">
            Manage your writing and track your progress
          </p>
        </div>

        {/* Mobile-First Stats Grid */}
        <div className="space-y-3 mb-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:space-y-0 lg:grid-cols-5">
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Subscribers</h3>
            <p className="text-2xl font-bold text-gray-900">{activeSubscriberCount}</p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Monthly Revenue</h3>
            <p className="text-2xl font-bold text-gray-900">{formatPrice(monthlyRevenue)}</p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Total Entries</h3>
            <p className="text-2xl font-bold text-gray-900">{totalEntries}</p>
          </div>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Total Views</h3>
            <p className="text-2xl font-bold text-gray-900">{totalViews.toLocaleString()}</p>
          </div>

          {/* Prominent Financial Info */}
          <div className={`rounded-lg p-4 border sm:col-span-2 lg:col-span-1 ${
            availableBalance > 0
              ? 'bg-green-50 border-green-200'
              : 'bg-orange-50 border-orange-200'
          }`}>
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide">Available Balance</h3>
            <div className={`text-3xl font-bold mb-2 ${
              availableBalance > 0 ? 'text-green-800' : 'text-orange-800'
            }`}>
              {formatPrice(availableBalance)}
            </div>
            {stripeBalance.pending > 0 && (
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-600">Pending</span>
                <span className="text-xs font-medium text-orange-600">{formatPrice(stripeBalance.pending)}</span>
              </div>
            )}
            <div className={`text-sm mb-3 ${
              availableBalance > 0 ? 'text-green-600' : 'text-orange-600'
            }`}>
              {availableBalance > 0 ? 'Ready to withdraw' : 'Processing payments'}
            </div>
            {availableBalance >= 1000 && (
              <button
                onClick={() => document.getElementById('withdrawal-section')?.scrollIntoView({ behavior: 'smooth' })}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors min-h-[44px]"
              >
                Withdraw Funds
              </button>
            )}
          </div>
        </div>

        {/* Mobile-First Content Sections */}
        <div className="space-y-4 w-full max-w-full">
          {/* Quick Actions - Priority on Mobile */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 w-full max-w-full overflow-hidden">
            <h2 className="text-lg font-serif text-gray-800 mb-4">Quick Actions</h2>

            {/* Primary Actions */}
            <div className="space-y-3 mb-4">
              <Button
                onClick={() => handleNavigation('/write')}
                disabled={navigating === '/write'}
                className="w-full bg-blue-600 text-white py-4 px-6 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors min-h-[48px]"
              >
                {navigating === '/write' ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    Creating...
                  </>
                ) : (
                  '📝 Create New Entry'
                )}
              </Button>

              <Button
                onClick={() => handleNavigation('/publishing')}
                disabled={navigating === '/publishing'}
                className="w-full bg-purple-600 text-white py-4 px-6 rounded-lg text-lg font-semibold hover:bg-purple-700 transition-colors min-h-[48px]"
              >
                {navigating === '/publishing' ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    Loading...
                  </>
                ) : (
                  '📚 Publishing Center'
                )}
              </Button>
            </div>

            {/* Secondary Actions - Consolidated */}
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={() => handleNavigation('/profile')}
                disabled={navigating === '/profile'}
                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg text-base hover:bg-gray-200 transition-colors border border-gray-200 min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/profile' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    Loading...
                  </>
                ) : (
                  'Edit Profile'
                )}
              </Button>

              <Button
                onClick={() => handleNavigation(`/u/${profile.id}`)}
                disabled={navigating === `/u/${profile.id}`}
                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg text-base hover:bg-gray-200 transition-colors border border-gray-200 min-h-[44px] flex items-center justify-center"
              >
                {navigating === `/u/${profile.id}` ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    Loading...
                  </>
                ) : (
                  'View Profile'
                )}
              </Button>
            </div>
          </div>

          {/* Profile Settings */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 w-full max-w-full overflow-hidden">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-serif text-gray-800">Profile Settings</h2>
              <Link
                href="/profile"
                className="bg-purple-600 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors min-h-[44px] flex items-center justify-center"
              >
                Edit
              </Link>
            </div>

            <div className="space-y-4">
              <div>
                <PriceEditor
                  initialPrice={profile.price_monthly || 999}
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 block mb-2">Payment Processing</label>
                <StripeConnectButton
                  isConnected={!!profile.stripe_account_id}
                  onboardingComplete={profile.stripe_onboarding_complete || false}
                />
                {!profile.stripe_account_id && (
                  <p className="text-xs text-gray-600 mt-2">
                    Connect Stripe to receive payments from subscribers.
                  </p>
                )}
              </div>

              <div className="w-full max-w-full overflow-hidden">
                <label className="text-sm font-medium text-gray-700 block mb-2">Your Profile URL</label>
                <div className="bg-gray-50 p-2 rounded border w-full max-w-full overflow-hidden" style={{ wordBreak: 'break-all' }}>
                  <code className="text-xs text-gray-800 break-all block w-full max-w-full overflow-hidden">
                    onlydiary.app/{profile.custom_url || `u/${profile.id}`}
                  </code>
                </div>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <Link
                    href={profile.custom_url ? `/${profile.custom_url}` : `/u/${profile.id}`}
                    className="bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700 transition-colors text-center min-h-[44px] flex items-center justify-center"
                  >
                    View Profile
                  </Link>
                  {!profile.custom_url ? (
                    <Link
                      href="/profile/edit"
                      className="bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm font-medium hover:bg-gray-200 transition-colors text-center min-h-[44px] flex items-center justify-center border border-gray-200"
                    >
                      Set Custom URL
                    </Link>
                  ) : (
                    <div className="bg-gray-50 px-3 py-2 rounded text-sm text-gray-500 text-center min-h-[44px] flex items-center justify-center border border-gray-200">
                      Custom URL Set
                    </div>
                  )}
                </div>
              </div>

              {profile.bio && (
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">Bio</label>
                  <p className="text-gray-700 text-sm bg-gray-50 p-2 rounded">{profile.bio}</p>
                </div>
              )}
            </div>
          </div>

        </div>

        {/* Revenue Boost Section - Optimized */}
        <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-lg p-4 border border-purple-100 w-full max-w-full overflow-hidden">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-lg">🔔</span>
            </div>
            <div>
              <h2 className="text-lg font-serif text-gray-800">💰 Boost Revenue</h2>
              <p className="text-sm text-gray-600">Push notifications to subscribers</p>
            </div>
          </div>

          <div className="bg-white rounded p-3 mb-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-800 text-sm">Waitlist Notifications</div>
                <div className="text-xs text-gray-600">$0.05 per notification</div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-purple-600">$0.05</div>
                <div className="text-xs text-gray-500">per send</div>
              </div>
            </div>
          </div>

          <Button
            onClick={() => handleNavigation('/dashboard/notifications')}
            disabled={navigating === '/dashboard/notifications'}
            className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg text-base font-medium hover:bg-purple-700 transition-colors min-h-[44px]"
          >
            {navigating === '/dashboard/notifications' ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Loading...
              </>
            ) : (
              'Get Started'
            )}
          </Button>
        </div>

        {/* Withdrawal Section */}
        <div id="withdrawal-section">
          <WithdrawalSection
            availableBalance={availableBalance}
            userId={profile.id}
          />
        </div>

        {/* Mailing List Manager */}
        <MailingListManager
          creatorId={profile.id}
          creatorName={profile.name || 'Creator'}
          customUrl={profile.custom_url}
        />

        {/* Manage Entries */}
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
          <h2 className="text-lg font-serif text-gray-800 mb-4">Manage Entries</h2>
          <EntriesManager initialEntries={profile.entries || []} />
        </div>

        </div>
      </div>
    </div>
  )
}
