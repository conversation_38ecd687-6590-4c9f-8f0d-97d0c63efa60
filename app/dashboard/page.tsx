"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { WithdrawalSection } from "@/components/WithdrawalSection"
import { StripeConnectButton } from "@/components/StripeConnectButton"
import { Button } from "@/components/ui/button"

import { InvitePrompt } from "@/components/InvitePrompt"
import { PriceEditor } from "@/components/PriceEditor"
import { EntriesManager } from "@/components/EntriesManager"
import { MailingListManager } from "@/components/MailingListManager"

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

export default function WriterDashboard() {
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<string | null>(null)
  const [stripeBalance, setStripeBalance] = useState<{
    available: number
    pending: number
    message?: string
  }>({ available: 0, pending: 0 })
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthAndFetchData()
  }, [])

  const checkAuthAndFetchData = async () => {
    try {
      // Check if user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("id", user.id)
        .single()

      if (profileError || !profileData) {
        console.log('Profile error or no profile:', profileError, profileData)
        router.push('/')
        return
      }

      console.log('User profile:', { id: profileData.id, role: profileData.role, name: profileData.name })

      // Redirect subscribers to their timeline
      if (profileData.role === 'subscriber') {
        console.log('Redirecting subscriber to timeline')
        router.push('/timeline')
        return
      }

      // Only writers and admins can access this dashboard
      if (profileData.role !== 'writer' && profileData.role !== 'admin') {
        console.log('Access denied - role is:', profileData.role)
        router.push('/')
        return
      }

      console.log('Dashboard access granted for role:', profileData.role)

      // Get writer's entries
      const { data: entries } = await supabase
        .from("diary_entries")
        .select("id, title, body_md, created_at, is_free, is_hidden, view_count")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      // Get subscription count
      const { data: subscriptions } = await supabase
        .from("subscriptions")
        .select("id, subscriber_id, active_until")
        .eq("writer_id", user.id)
        .gte("active_until", new Date().toISOString())

      // Get payments for earnings calculation
      const { data: payments } = await supabase
        .from("payments")
        .select("amount_cents, kind, created_at, stripe_payment_id")
        .eq("writer_id", user.id)
        .order("created_at", { ascending: false })

      console.log('All payments:', payments)

      // Get withdrawals for balance calculation
      const { data: withdrawals } = await supabase
        .from("withdrawals")
        .select("amount_cents, status")
        .eq("writer_id", user.id)
        .neq("status", "rejected")

      setProfile({
        ...profileData,
        entries: entries || [],
        subscriptions: subscriptions || [],
        payments: payments || [],
        withdrawals: withdrawals || []
      })

      // Fetch real Stripe Connect balance
      await fetchStripeBalance()
    } catch (error) {
      console.error('Error in checkAuthAndFetchData:', error)
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const fetchStripeBalance = async () => {
    try {
      console.log('Fetching Stripe balance...')
      const response = await fetch('/api/stripe/balance')
      const data = await response.json()

      console.log('Stripe balance response:', { status: response.status, data })

      if (response.ok) {
        setStripeBalance({
          available: data.available,
          pending: data.pending,
          message: data.message
        })
        console.log('Stripe balance set:', data.available)
      } else {
        console.error('Error fetching Stripe balance:', data.error)
        setStripeBalance({ available: 0, pending: 0, message: data.error })
      }
    } catch (error) {
      console.error('Error fetching Stripe balance:', error)
      setStripeBalance({ available: 0, pending: 0, message: 'Failed to fetch balance' })
    }
  }

  const handleNavigation = async (path: string) => {
    setNavigating(path)
    router.push(path)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Dashboard...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load dashboard</p>
        </div>
      </div>
    )
  }

  const activeSubscriberCount = profile.subscriptions?.length || 0
  const monthlyRevenue = activeSubscriberCount * (profile.price_monthly || 0)
  const totalEntries = profile.entries?.length || 0
  const totalViews = profile.entries?.reduce((sum: number, entry: any) => sum + (entry.view_count || 0), 0) || 0


  // Use real Stripe Connect balance instead of manual calculations
  const availableBalance = stripeBalance.available // This is the actual withdrawable amount from Stripe
  const pendingBalance = stripeBalance.pending // Money that's processing

  // Keep total earnings for display purposes
  const totalEarnings = profile.payments?.reduce((sum: number, p: any) => sum + p.amount_cents, 0) || 0

  return (
    <div className="min-h-screen bg-gray-50 overflow-x-hidden">
      <div className="w-full max-w-none sm:max-w-7xl mx-auto px-1 sm:px-4 lg:px-6 py-2 sm:py-4 lg:py-6">
        {/* Clean Header */}
        <div className="mb-3 sm:mb-4 lg:mb-6">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-serif text-gray-900 mb-1">
            Welcome back, {profile.name || 'Writer'}
          </h1>
          <p className="text-gray-600 font-serif text-xs sm:text-sm">
            Manage your writing and track your progress
          </p>
        </div>

        {/* Mobile-Optimized Stats */}
        <div className="w-full overflow-x-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-2 sm:gap-3 mb-3 sm:mb-4 lg:mb-6 min-w-0">
          <div className="bg-white rounded-lg p-2 sm:p-3 shadow-sm border border-gray-100 min-h-[70px] flex flex-col justify-center w-full min-w-0">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide truncate">Subscribers</h3>
            <p className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate">{activeSubscriberCount}</p>
          </div>

          <div className="bg-white rounded-lg p-2 sm:p-3 shadow-sm border border-gray-100 min-h-[70px] flex flex-col justify-center w-full min-w-0">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide truncate">Revenue</h3>
            <p className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate">{formatPrice(monthlyRevenue)}</p>
          </div>

          <div className="bg-white rounded-lg p-2 sm:p-3 shadow-sm border border-gray-100 min-h-[70px] flex flex-col justify-center w-full min-w-0">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide truncate">Entries</h3>
            <p className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate">{totalEntries}</p>
          </div>

          <div className="bg-white rounded-lg p-2 sm:p-3 shadow-sm border border-gray-100 min-h-[70px] flex flex-col justify-center w-full min-w-0">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide truncate">Views</h3>
            <p className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate">{totalViews.toLocaleString()}</p>
          </div>

          <div className="bg-white rounded-lg p-2 sm:p-3 shadow-sm border border-gray-100 min-h-[70px] flex flex-col justify-center w-full min-w-0">
            <h3 className="text-xs font-medium text-gray-500 mb-1 uppercase tracking-wide truncate">Balance</h3>
            <div className="space-y-1 w-full min-w-0">
              <div className="flex items-center justify-between w-full">
                <span className="text-xs text-gray-600 truncate">Available</span>
                <span className="text-base sm:text-lg font-bold text-gray-900 truncate">{formatPrice(availableBalance)}</span>
              </div>
              {stripeBalance.pending > 0 && (
                <div className="flex items-center justify-between w-full">
                  <span className="text-xs text-gray-500 truncate">Pending</span>
                  <span className="text-xs font-medium text-orange-600 truncate">{formatPrice(stripeBalance.pending)}</span>
                </div>
              )}
            </div>
            <p className="text-xs text-gray-400 mt-1 truncate">
              {availableBalance > 0 ? 'Ready to withdraw' : 'Processing'}
            </p>
            {availableBalance >= 1000 && (
              <button
                onClick={() => document.getElementById('withdrawal-section')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-xs text-purple-600 hover:text-purple-800 mt-1 font-medium bg-purple-50 px-2 py-1 rounded truncate"
              >
                Withdraw →
              </button>
            )}
          </div>
          </div>
        </div>

        <div className="w-full overflow-x-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4 lg:mb-6 min-w-0">
          {/* Profile Summary */}
          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100 w-full min-w-0">
            <div className="flex items-center justify-between mb-3 w-full min-w-0">
              <h2 className="text-base sm:text-lg font-serif text-gray-800 truncate">Profile</h2>
              <Link
                href="/profile"
                className="text-xs sm:text-sm text-purple-600 hover:text-purple-800 font-medium bg-purple-50 px-2 py-1 rounded flex-shrink-0"
              >
                Edit →
              </Link>
            </div>

            <div className="space-y-3 sm:space-y-4">
              <div>
                <PriceEditor
                  initialPrice={profile.price_monthly || 999}
                />
              </div>

              <div>
                <label className="text-xs sm:text-sm font-medium text-gray-500">Payment Processing</label>
                <div className="mt-2">
                  <StripeConnectButton
                    isConnected={!!profile.stripe_account_id}
                    onboardingComplete={profile.stripe_onboarding_complete || false}
                  />
                </div>
                {!profile.stripe_account_id && (
                  <p className="text-xs text-gray-500 mt-1">
                    Connect Stripe to receive payments from subscribers
                  </p>
                )}
              </div>

              <div>
                <label className="text-xs sm:text-sm font-medium text-gray-500">Your Profile URL</label>
                <div className="mt-1">
                  <code className="text-xs sm:text-sm bg-gray-100 px-2 py-1 rounded text-gray-800 break-all">
                    onlydiary.app/{profile.custom_url || `u/${profile.id}`}
                  </code>
                </div>
                <Link
                  href={profile.custom_url ? `/${profile.custom_url}` : `/u/${profile.id}`}
                  className="block text-blue-600 hover:text-blue-800 text-xs sm:text-sm mt-1"
                >
                  View your public profile →
                </Link>
                {!profile.custom_url && (
                  <Link
                    href="/profile/edit"
                    className="block text-gray-500 hover:text-gray-700 text-xs mt-1"
                  >
                    Set custom URL →
                  </Link>
                )}
              </div>

              {profile.bio && (
                <div>
                  <label className="text-xs sm:text-sm font-medium text-gray-500">Bio</label>
                  <p className="text-gray-700 text-xs sm:text-sm leading-relaxed">{profile.bio}</p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100 w-full min-w-0">
            <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-3 truncate">Quick Actions</h2>

            <div className="space-y-3">
              <Button
                onClick={() => handleNavigation('/write')}
                disabled={navigating === '/write'}
                className="w-full bg-gray-900 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors"
              >
                {navigating === '/write' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Creating...
                  </>
                ) : (
                  '✍️ Create Content'
                )}
              </Button>

              <Button
                onClick={() => handleNavigation('/publishing')}
                disabled={navigating === '/publishing'}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors"
              >
                {navigating === '/publishing' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Loading...
                  </>
                ) : (
                  '📚 Publishing Center'
                )}
              </Button>

              <Button
                onClick={() => handleNavigation('/dashboard/notifications')}
                disabled={navigating === '/dashboard/notifications'}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
              >
                {navigating === '/dashboard/notifications' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Loading...
                  </>
                ) : (
                  '🔔 Waitlist Notifications'
                )}
              </Button>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <Button
                  onClick={() => handleNavigation('/profile')}
                  disabled={navigating === '/profile'}
                  className="w-full bg-gray-50 text-gray-700 py-2 px-3 rounded-lg text-xs sm:text-sm font-medium hover:bg-gray-100 transition-colors border border-gray-200"
                >
                  {navigating === '/profile' ? (
                    <>
                      <div className="w-3 h-3 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                      Loading...
                    </>
                  ) : (
                    '✏️ Edit Profile'
                  )}
                </Button>

                <Button
                  onClick={() => handleNavigation(`/u/${profile.id}`)}
                  disabled={navigating === `/u/${profile.id}`}
                  className="w-full bg-gray-50 text-gray-700 py-2 px-3 rounded-lg text-xs sm:text-sm font-medium hover:bg-gray-100 transition-colors border border-gray-200"
                >
                  {navigating === `/u/${profile.id}` ? (
                    <>
                      <div className="w-3 h-3 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                      Loading...
                    </>
                  ) : (
                    '👁️ View Profile'
                  )}
                </Button>
              </div>

              {/* Invite Friends */}
              <div className="mt-4">
                <InvitePrompt variant="card" userName={profile.name} />
              </div>
            </div>
          </div>

          {/* Promotions Section */}
          <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-lg p-4 sm:p-5 border border-purple-100">
            <h2 className="text-lg font-serif text-gray-800 mb-3">💰 Boost Your Revenue</h2>
            <p className="text-gray-600 text-sm mb-4">
              Turn your waitlists into instant sales with push notifications
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-lg">🔔</span>
                </div>
                <div>
                  <div className="font-medium text-gray-800">Waitlist Notifications</div>
                  <div className="text-sm text-gray-600">$0.05 per notification • 1-999 credits</div>
                </div>
              </div>

              <Button
                onClick={() => handleNavigation('/dashboard/notifications')}
                disabled={navigating === '/dashboard/notifications'}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors"
              >
                {navigating === '/dashboard/notifications' ? (
                  <>
                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                    Loading...
                  </>
                ) : (
                  'Learn More'
                )}
              </Button>
            </div>
          </div>

          {/* Withdrawal Section */}
          <div id="withdrawal-section">
            <WithdrawalSection
              availableBalance={availableBalance}
              userId={profile.id}
            />
          </div>

          {/* Mailing List Manager */}
          <div className="mt-6">
            <MailingListManager
              creatorId={profile.id}
              creatorName={profile.name || 'Creator'}
              customUrl={profile.custom_url}
            />
          </div>
        </div>
        </div>

        {/* Manage Entries */}
        <div className="mt-6 sm:mt-8">
          <div className="bg-white rounded-lg p-4 sm:p-5 shadow-sm border border-gray-100">
            <h2 className="text-lg font-serif text-gray-800 mb-4">Manage Entries</h2>

            <EntriesManager initialEntries={profile.entries || []} />
          </div>
        </div>


      </div>
    </div>
  )
}
